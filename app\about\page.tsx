"use client"

import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Brain, Cpu, GraduationCap, Phone, Mail, MapPin, Target, Users, Award, TrendingUp, ArrowRight } from "lucide-react"
import Link from "next/link"

// 核心业务介绍
const coreBusinesses = [
  {
    title: "AI智能标注",
    content: "提供专业的AI数据标注服务，支持图像、文本、语音等多模态数据标注，拥有500+专业标注团队，为机器学习模型提供高质量训练数据。",
    icon: Brain,
    stats: "1000万+ 标注数据",
    features: ["图像标注", "文本标注", "语音标注", "质量控制"],
    color: "from-blue-500 to-cyan-500",
    bgColor: "from-blue-50 to-cyan-50"
  },
  {
    title: "CPU算力租用",
    content: "灵活的云计算资源租用服务，提供高性能CPU集群，支持科学计算、深度学习训练等高算力需求，按需付费，弹性扩容。",
    icon: Cpu,
    stats: "50,000+ CPU核心",
    features: ["高性能计算", "弹性扩容", "按需付费", "24/7监控"],
    color: "from-green-500 to-emerald-500",
    bgColor: "from-green-50 to-emerald-50"
  },
  {
    title: "教育培训管理",
    content: "一站式教育培训管理平台，涵盖课程管理、学员管理、在线考试、证书颁发等完整教育生态，服务50,000+学员。",
    icon: GraduationCap,
    stats: "50,000+ 服务学员",
    features: ["课程管理", "在线考试", "学员跟踪", "证书系统"],
    color: "from-purple-500 to-violet-500",
    bgColor: "from-purple-50 to-violet-50"
  }
]

// 公司时间线
const timeline = [
  {
    year: "2025",
    title: "公司成立",
    description: "零点科技在北京成立，专注AI技术研发",
    icon: "🚀"
  },
  {
    year: "2025",
    title: "产品规划",
    description: "制定AI智能标注平台发展战略，组建核心技术团队",
    icon: "🎯"
  },
  {
    year: "2026",
    title: "产品发布",
    description: "推出首款AI智能标注平台，获得市场认可",
    icon: "📈"
  },
  {
    year: "2027",
    title: "业务扩展",
    description: "新增CPU算力租用服务，进入云计算领域",
    icon: "☁️"
  },
  {
    year: "2028",
    title: "教育布局",
    description: "推出教育培训管理系统，进入教育科技领域",
    icon: "🎓"
  }
]

// 团队成员
const teamMembers = [
  {
    name: "张伟",
    position: "创始人 & CEO",
    avatar: "👨‍💼",
    description: "15年AI行业经验，前阿里巴巴技术专家",
    social: { linkedin: "#", twitter: "#" }
  },
  {
    name: "李娜",
    position: "技术总监",
    avatar: "👩‍💻",
    description: "AI算法专家，发表论文20+篇",
    social: { linkedin: "#", twitter: "#" }
  },
  {
    name: "王强",
    position: "产品总监",
    avatar: "👨‍🎨",
    description: "产品设计专家，用户体验优化大师",
    social: { linkedin: "#", twitter: "#" }
  },
  {
    name: "赵敏",
    position: "运营总监",
    avatar: "👩‍💼",
    description: "市场运营专家，客户成功率98%+",
    social: { linkedin: "#", twitter: "#" }
  }
]

// 公司价值观
const values = [
  {
    title: "创新驱动",
    description: "持续技术创新，引领行业发展",
    icon: "💡",
    color: "from-yellow-400 to-orange-500"
  },
  {
    title: "客户至上",
    description: "以客户需求为中心，提供优质服务",
    icon: "❤️",
    color: "from-red-400 to-pink-500"
  },
  {
    title: "团队协作",
    description: "团结协作，共同成长，共创辉煌",
    icon: "🤝",
    color: "from-blue-400 to-indigo-500"
  },
  {
    title: "诚信负责",
    description: "诚信经营，承担社会责任",
    icon: "🛡️",
    color: "from-green-400 to-teal-500"
  }
]

// 企业优势
const advantages = [
  {
    title: "专业团队",
    description: "拥有200+技术专家，覆盖AI、云计算、教育科技等多个领域",
    icon: Users,
    stats: "200+ 专家"
  },
  {
    title: "服务客户",
    description: "已为2000+企业客户提供专业服务，涵盖多个行业",
    icon: Target,
    stats: "2000+ 客户"
  },
  {
    title: "行业认证",
    description: "获得ISO27001、SOC2等多项国际认证，确保服务质量",
    icon: Award,
    stats: "10+ 认证"
  },
  {
    title: "业务增长",
    description: "连续3年保持100%+业务增长，行业领先地位稳固",
    icon: TrendingUp,
    stats: "100%+ 增长"
  }
]

// 联系方式
const contactInfo = {
  phone: {
    label: "全国服务热线",
    value: "************",
    icon: Phone,
  },
  email: {
    label: "商务合作邮箱",
    value: "<EMAIL>",
    icon: Mail,
  },
  address: {
    label: "总部地址",
    value: "北京市朝阳区科技园区88号零点大厦",
    icon: MapPin,
  }
}

export default function About() {
  return (
    <div className="min-h-screen">
      {/* Enhanced Hero Section */}
      <section className="relative py-24 sm:py-32 overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/3 to-transparent" />
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="grid-bg absolute inset-0 opacity-30" />
        </div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-4xl text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-6"
            >
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full border border-primary/20 mb-6">
                <span className="text-2xl">🚀</span>
                <span className="text-sm font-medium text-primary">成立于2025年</span>
              </div>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6"
            >
              <span className="text-gradient-modern">关于零点科技</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-xl leading-8 text-muted-foreground mb-8 max-w-3xl mx-auto"
            >
              专注AI智能标注、CPU算力租用、教育培训管理三大核心业务，为企业数字化转型提供专业技术服务，致力于成为全球领先的AI技术服务提供商
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-wrap justify-center gap-8 text-center"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="text-3xl font-bold text-primary mb-1">2000+</div>
                <div className="text-sm text-muted-foreground">服务客户</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="text-3xl font-bold text-primary mb-1">200+</div>
                <div className="text-sm text-muted-foreground">专业团队</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="text-3xl font-bold text-primary mb-1">新创</div>
                <div className="text-sm text-muted-foreground">创新企业</div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Businesses Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              核心业务
            </h2>
            <p className="text-lg leading-8 text-muted-foreground">
              三大核心业务领域，为不同行业客户提供专业解决方案
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {coreBusinesses.map((business, index) => (
              <motion.div
                key={business.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <div className={`absolute inset-0 bg-gradient-to-br ${business.bgColor} opacity-0 group-hover:opacity-50 transition-opacity duration-500`} />
                  <CardContent className="p-8 relative">
                    <div className="flex items-center gap-4 mb-6">
                      <div className={`rounded-2xl bg-gradient-to-br ${business.color} p-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                        <business.icon className="h-8 w-8 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">{business.title}</h3>
                        <p className="text-sm text-primary font-medium">{business.stats}</p>
                      </div>
                    </div>
                    <p className="text-muted-foreground mb-6 leading-relaxed">{business.content}</p>
                    <div className="flex flex-wrap gap-2">
                      {business.features.map((feature) => (
                        <span
                          key={feature}
                          className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary border border-primary/20"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Timeline */}
      <section className="py-24 sm:py-32 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              发展历程
            </h2>
            <p className="text-lg leading-8 text-muted-foreground">
              从初创到行业领先，见证我们的成长足迹
            </p>
          </motion.div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-primary/20 via-primary to-primary/20"></div>
            <div className="space-y-12">
              {timeline.map((item, index) => (
                <motion.div
                  key={item.year}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                >
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-3 mb-3">
                          <span className="text-2xl">{item.icon}</span>
                          <div>
                            <h3 className="text-lg font-semibold">{item.title}</h3>
                            <p className="text-sm text-primary font-medium">{item.year}</p>
                          </div>
                        </div>
                        <p className="text-muted-foreground">{item.description}</p>
                      </CardContent>
                    </Card>
                  </div>
                  <div className="relative z-10 flex items-center justify-center w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-full shadow-lg">
                    <div className="w-4 h-4 bg-white rounded-full"></div>
                  </div>
                  <div className="w-1/2"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              企业价值观
            </h2>
            <p className="text-lg leading-8 text-muted-foreground">
              指引我们前进的核心理念
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group text-center"
              >
                <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <CardContent className="p-8">
                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${value.color} shadow-lg group-hover:scale-110 transition-transform duration-300 mb-6`}>
                      <span className="text-2xl">{value.icon}</span>
                    </div>
                    <h3 className="text-lg font-semibold mb-3 group-hover:text-primary transition-colors">{value.title}</h3>
                    <p className="text-muted-foreground text-sm leading-relaxed">{value.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-24 sm:py-32 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              核心团队
            </h2>
            <p className="text-lg leading-8 text-muted-foreground">
              汇聚行业精英，共创美好未来
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {teamMembers.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <CardContent className="p-8 text-center">
                    <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center text-4xl group-hover:scale-110 transition-transform duration-300">
                      {member.avatar}
                    </div>
                    <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">{member.name}</h3>
                    <p className="text-sm text-primary font-medium mb-3">{member.position}</p>
                    <p className="text-sm text-muted-foreground leading-relaxed">{member.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Advantages Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              企业优势
            </h2>
            <p className="text-lg leading-8 text-muted-foreground">
              专业实力与服务品质的完美结合
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {advantages.map((advantage, index) => (
              <motion.div
                key={advantage.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group text-center"
              >
                <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <CardContent className="p-8">
                    <div className="mx-auto mb-6 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 p-4 w-fit group-hover:scale-110 transition-transform duration-300">
                      <advantage.icon className="h-8 w-8 text-primary" />
                    </div>
                    <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">{advantage.title}</h3>
                    <p className="text-3xl font-bold text-primary mb-3">{advantage.stats}</p>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {advantage.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 sm:py-32 bg-gradient-to-br from-primary/5 to-primary/10">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              联系我们
            </h2>
            <p className="text-lg leading-8 text-muted-foreground">
              期待与您合作，共创美好未来
            </p>
          </motion.div>

          {/* Contact Cards */}
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-3 mb-16">
            {Object.values(contactInfo).map((info, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 text-center h-full">
                  <CardContent className="p-8">
                    <div className="mx-auto mb-6 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 p-4 w-fit group-hover:scale-110 transition-transform duration-300">
                      <info.icon className="h-6 w-6 text-primary" />
                    </div>
                    <h3 className="text-sm font-semibold text-muted-foreground mb-2">
                      {info.label}
                    </h3>
                    <p className="text-base font-medium">{info.value}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* CTA Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row items-center justify-center gap-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <Link href="/products">
              <Button className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 px-8 py-3 text-lg">
                浏览产品
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
            <Link href="/contact-us">
              <Button className="px-8 py-3 text-lg border border-primary/20 bg-transparent hover:bg-primary/5 text-foreground">
                联系销售
                <Phone className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

